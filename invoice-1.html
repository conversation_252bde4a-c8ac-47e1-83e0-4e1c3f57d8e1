<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #fff;
            color: #333;
            font-size: 16px;
            --accent-color: #ff5959;
            --background-color: #ff59593c;
        }

        p {
            margin: 0;
            line-height: 1.2;
        }

        /* Make empty paragraphs create proper spacing */
        p:empty {
            height: 16px;
            display: block;
        }

        .invoice-container {
            max-width: 800px;
            margin: auto 32px;
            background: white;
            padding: 48px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .invoice-header,
        .client-details,
        .summary,
        .notes {
            margin-bottom: 20px;
        }

        .invoice-header h1 {
            color: var(--accent-color);
            font-size: 36px;
        }

        .invoice-header p {
            margin: 0;
            line-height: 24px;
        }

        .client-details p {
            margin: 0;
            line-height: 24px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .items-table th {
            background-color: #f8f8f8;
        }

        .summary {
            text-align: right;
        }

        .summary p {
            margin: 10px 0;
        }

        .final-amount {
            font-weight: bold;
            font-size: 24px;
            background-color: var(--background-color);
            padding: 8px;
        }

        .top-line-container {
            background-color: var(--background-color);
            padding: 2px 8px;
        }

        .top-line {
            font-weight: bold;
            font-size: 12px;
            line-height: 1.5;
            /* Increased from 8px to provide proper spacing */
            margin: 4px 0;
            /* Add vertical margin between lines */
        }

        .coupon {
            font-weight: bold;
            font-size: 12px;
            background-color: var(--background-color);
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 8px;
            position: relative;
            bottom: 2px;
        }

        a {
            color: #666;
            /* Slightly lighter than the regular text color (#333) */
            text-decoration: underline;
            text-decoration-color: var(--accent-color);
            text-decoration-thickness: 2px;
            text-underline-offset: 2px;
            /* Space between text and underline */
        }

        /* Class for adding vertical spacing */
        .spacer {
            height: 16px;
            display: block;
        }

        /* Payment button styling */
        .payment-link {
            text-align: center;
            margin: 30px 0;
        }

        .payment-link a {
            display: inline-block;
            background-color: var(--accent-color);
            color: white;
            font-weight: bold;
            padding: 16px 32px;
            margin: 16px;
            font-size: 20px;
            border-radius: 8px;
            text-transform: uppercase;
            letter-spacing: 1px;
            text-decoration: none;
            /* Override the general link styling */
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 600px) {
            .invoice-container {
                width: 90%;
                padding: 20px 20px;
            }

            .items-table,
            .items-table th,
            .items-table td {
                font-size: 12px;
            }

            .top-line,
            .final-amount {
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div style="height: 50px"></div>
    <div class="invoice-container">
        <div class="top-line-container">
            <p class="top-line">INVOICE: INV-{* INVOICE_PREFIX *}-{* INVOICE_NUMBER *}</p>
            <p class="top-line">DATE: {* INVOICE_DATE *}</p>
            <p class="top-line">DUE DATE: {* INVOICE_DUE_DATE *}</p>
        </div>
        <header class="invoice-header">
            <h1>{* BUSINESS_NAME *}</h1>
            <p>{* PAYTO_ADDRESS_LINE_1 *}</p>
            <p>{* PAYTO_ADDRESS_LINE_2 *}</p>
            <p>{* PAYTO_TAX_ID *}</p>
            <p>Email: <a href="mailto:{* PAYTO_EMAIL *}">{* PAYTO_EMAIL *}</a></p>
            <p>Tel: <a href="tel:{* PAYTO_TEL *}">{* PAYTO_TEL *}</a></p>
        </header>
        <section class="client-details">
            <h2>Bill To:</h2>
            <p>{* BILL_TO_BUSINESS_NAME_OR_CUSTOMER_NAME *}</p>
            <p>{* BILLTO_ADDRESS_LINE_1 *}</p>
            <p>{* BILLTO_ADDRESS_LINE_2 *}</p>
            <p>{* BILLTO_TAX_ID *}</p>
            <p>Email: <a href="mailto:{* BILLTO_EMAIL *}">{* BILLTO_EMAIL *}</a></p>
            <p>Tel: <a href="tel:{* BILLTO_TEL *}">{* BILLTO_TEL *}</a></p>
        </section>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item Description</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{* ITEM1_DESCRIPTION *}</td>
                    <td>{* ITEM1_QUANTITY *}</td>
                    <td>{* ITEM1_PRICE *}</td>
                    <td>{* ITEM1_AMOUNT *}</td>
                </tr>
                <tr>
                    <td>{* ITEM2_DESCRIPTION *}</td>
                    <td>{* ITEM2_QUANTITY *}</td>
                    <td>{* ITEM2_PRICE *}</td>
                    <td>{* ITEM2_AMOUNT *}</td>
                </tr>
                <!-- Add more items as needed -->
            </tbody>
        </table>
        <section class="summary">
            <p>Subtotal: {* SUBTOTAL *}</p>
            {* DISCOUNT *}
            <div class="spacer"></div>{* TAX *}
            <p class="final-amount">Total: {* TOTAL *}</p>
        </section>
        <p class="payment-link"><a target="_blank" rel="noopener noreferrer" href="HTTPS://STRING.SYSTEMS">CLICK HERE TO
                PAY ONLINE</a>
        </p>
        <section class="notes">
            <p>{* NOTES *}</p>
        </section>
    </div>
</body>

</html>