<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #fff;
            color: #333;
            font-size: 16px;
        }

        .invoice-container {
            max-width: 800px;
            margin: auto 32px;
            background: white;
            padding: 32px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .invoice-header,
        .client-details,
        .summary {
            margin-bottom: 20px;
        }

        .invoice-header h1 {
            color: #ff5959;
            font-size: 36px;
        }

        .invoice-header p {
            margin: 0;
            line-height: 24px;
        }

        .client-details p {
            margin: 0;
            line-height: 24px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .items-table th {
            background-color: #f8f8f8;
        }

        .summary p {
            margin: 10px 0;
        }

        .final-amount {
            font-weight: bold;
            font-size: 24px;
            background-color: #ff59593c;
            padding: 8px;
        }

        .top-line-container {
            background-color: #ff59593c;
            padding: 2px 8px;
        }

        .top-line {
            font-weight: bold;
            font-size: 12px;
            line-height: 8px;
        }

        .coupon {
            font-weight: bold;
            font-size: 12px;
            background-color: #ff59593c;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 8px;
            position: relative;
            bottom: 2px;
        }

        @media (max-width: 600px) {
            .invoice-container {
                width: 90%;
                padding: 20px 20px;
            }

            .items-table,
            .items-table th,
            .items-table td {
                font-size: 12px;
            }

            .top-line,
            .final-amount {
                text-align: center;
            }
        }
    </style>
</head>

<body>
    <div style="height: 50px"></div>
    <div class="invoice-container">
        <div class="top-line-container">
            <p class="top-line">DATE: {* DOP_INVOICEDATE *}</p>
            <p class="top-line">INVOICE: #{* DOP_INVOICENUMBER *}</p>
        </div>
        <header class="invoice-header">
            <h1>OnTime Appointments</h1>
            <p>String Systems aka Effective Music Practice LTD</p>
            <p>VAT No: CY10399982P</p>
            <p>Nikolaou Skoufa 29, Limassol, Cyprus 3016</p>
            <p>Email: <EMAIL> | Tel: (357) 99302141</p>
        </header>
        <section class="client-details">
            <h2>Invoice To:</h2>
            <p>{* DOP_CLIENTNAME *}</p>
            {* DOP_CLIENTBUSINESS *}
            <p>{* DOP_CLIENTADDRESS *}</p>
        </section>
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item Description</th>
                    <th>Quantity</th>
                    <th>Price</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{* DOP_SUBSCRIPTIONDESCRIPTION *}</td>
                    <td>{* DOP_SUBSCRIPTIONQUANTITY *}</td>
                    <td>{* DOP_SUBSCRIPTIONPRICE *}</td>
                    <td>{* DOP_SUBSCRIPTIONAMOUNT *}</td>
                </tr>
                <tr>
                    <td>{* DOP_SEATSDESCRIPTION *}</td>
                    <td>{* DOP_SEATSQUANTITY *}</td>
                    <td>{* DOP_SEATSPRICE *}</td>
                    <td>{* DOP_SEATSAMOUNT *}</td>
                </tr>
                <!-- Add more items as needed -->
            </tbody>
        </table>
        <section class="summary">
            <p>Subtotal: {* DOP_SUBTOTAL *}</p>
            {* DOP_DISCOUNT *}
            <p></p>{* DOP_TAX *}
            <p class="final-amount">Total: {* DOP_TOTAL *}</p>
        </section>
    </div>
    <div style="height: 50px"></div>
</body>

</html>